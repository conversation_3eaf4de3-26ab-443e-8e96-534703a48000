import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
  Modal,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { mealsAPI, userAPI, mealPlansAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { useFavorites } from '../../context/FavoritesContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const { width } = Dimensions.get('window');

const MealDetailScreen = ({ navigation, route }) => {
  // Add null checking for route.params
  const hasValidParams = route.params && route.params.meal;
  const { meal: initialMeal } = hasValidParams ? route.params : { meal: null };

  // Always call hooks at the top level - no conditional returns before hooks
  const [meal, setMeal] = useState(initialMeal);
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  // Quick meal picker modal state
  const [showMealPicker, setShowMealPicker] = useState(false);
  const [selectedDates, setSelectedDates] = useState([]);
  const [selectedMealTypes, setSelectedMealTypes] = useState([]);
  const [isCreatingMealPlan, setIsCreatingMealPlan] = useState(false);

  // Serving size state
  const [servingSize, setServingSize] = useState(1);

  const { user } = useAuth();
  const { isFavorite, addFavorite, removeFavorite } = useFavorites();

  // Always call hooks at the top level
  React.useEffect(() => {
    if (!hasValidParams) {
      console.error('MealDetailScreen: Missing meal parameter in route.params');
      navigation.goBack();
      return;
    }

    if (initialMeal && (initialMeal.id || initialMeal._id)) {
      loadMealDetails();
    }
    // Track meal view for history
    trackMealView();
  }, [hasValidParams, navigation]);

  // Early return after all hooks have been called
  if (!hasValidParams) {
    return null;
  }

  // Show loading only if we have no meal data at all and are currently loading
  if (!meal && loading) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.centerContent}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={commonStyles.loadingText}>Loading meal details...</Text>
        </View>
      </SafeAreaView>
    );
  }

  // If we have no meal data and not loading, show error
  if (!meal) {
    return (
      <SafeAreaView style={commonStyles.container}>
        <View style={commonStyles.centerContent}>
          <Ionicons name="alert-circle-outline" size={64} color={colors.textSecondary} />
          <Text style={commonStyles.errorText}>Unable to load meal details</Text>
          <TouchableOpacity
            style={commonStyles.retryButton}
            onPress={() => navigation.goBack()}
          >
            <Text style={commonStyles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  const trackMealView = async () => {
    if (!initialMeal) {
      console.log('No initial meal to track');
      return;
    }

    try {
      console.log('=== TRACKING MEAL VIEW ===');
      console.log('Initial meal data:', JSON.stringify(initialMeal, null, 2));

      const response = await userAPI.addRecentlyViewedMeal({
        meal: initialMeal
      });

      console.log('Track meal view response:', JSON.stringify(response, null, 2));
      console.log(`Successfully tracked view for meal: ${initialMeal.name}`);
    } catch (error) {
      console.error('Error tracking meal view:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
      // Don't fail the screen if tracking fails
    }
  };

  const loadMealDetails = async () => {
    if (!initialMeal) return;

    // Check if we already have complete meal data
    const hasCompleteData = initialMeal.ingredients && initialMeal.instructions && initialMeal.description;
    if (hasCompleteData) {
      console.log('Meal data is already complete, skipping API call');
      return;
    }

    // Check if we have an ID to fetch more details
    const mealId = initialMeal.id || initialMeal._id || initialMeal.mealId;
    if (!mealId) {
      console.log('No meal ID found, using existing data');
      return;
    }

    try {
      setLoading(true);
      console.log('Loading meal details for ID:', mealId);
      const response = await mealsAPI.getMealById(mealId);

      if (response.data) {
        console.log('Successfully loaded meal details');
        setMeal(response.data);
      } else {
        console.log('No meal data returned from API, keeping existing data');
      }
    } catch (error) {
      console.error('Error loading meal details:', error);
      console.log('API call failed, keeping existing meal data');
      // Don't show error to user, just keep the existing meal data
    } finally {
      setLoading(false);
    }
  };

  const handleFavoritePress = async () => {
    try {
      const mealId = meal.id || meal._id;
      if (isFavorite(mealId)) {
        await removeFavorite(mealId);
      } else {
        await addFavorite(meal);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to update favorites');
    }
  };

  const handleAddToPlan = () => {
    setSelectedDates([]);
    setSelectedMealTypes([]);
    setShowMealPicker(true);
  };

  const toggleDateSelection = (date) => {
    setSelectedDates(prev =>
      prev.includes(date)
        ? prev.filter(d => d !== date)
        : [...prev, date]
    );
  };

  const toggleMealTypeSelection = (mealType) => {
    setSelectedMealTypes(prev =>
      prev.includes(mealType)
        ? prev.filter(mt => mt !== mealType)
        : [...prev, mealType]
    );
  };

  // Helper function to get valid meal ID for backend
  const getMealIdForBackend = (meal) => {
    // The backend meals array expects ObjectId references
    // Prioritize _id over id, and ensure it's a valid MongoDB ObjectId
    const mealId = meal._id;

    if (!mealId) {
      console.error('No _id found in meal object:', meal);
      throw new Error('Meal object must have a valid _id property');
    }

    // Ensure it's a valid ObjectId format (24 character hex string)
    if (typeof mealId !== 'string' || mealId.length !== 24) {
      console.error('Invalid ObjectId format:', mealId);
      throw new Error('Meal _id must be a valid 24-character ObjectId');
    }

    console.log('Meal ID being sent:', mealId, 'Type:', typeof mealId, 'Original meal:', meal?.name || 'Unknown');
    return mealId;
  };

  // Serving size helper functions
  const incrementServingSize = () => {
    setServingSize(prev => Math.min(prev + 1, 4)); // Max 4 servings
  };

  const decrementServingSize = () => {
    setServingSize(prev => Math.max(prev - 1, 1)); // Min 1 serving
  };

  // Function to adjust ingredient quantities based on serving size
  const adjustIngredientQuantity = (ingredient) => {
    if (!ingredient || servingSize === 1) return ingredient;

    // Try to extract quantity and unit from ingredient string
    const patterns = [
      /^(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)$/,  // "2 cups flour"
      /^(\d+(?:\.\d+)?)\s+(.+)$/,           // "2 eggs"
      /^(\d+\/\d+)\s*(\w+)?\s+(.+)$/,       // "1/2 cup sugar"
    ];

    for (const pattern of patterns) {
      const match = ingredient.match(pattern);
      if (match) {
        let quantity = match[1];
        const unit = match[2] || '';
        const item = match[3] || match[2];

        // Handle fractions
        if (quantity.includes('/')) {
          const [numerator, denominator] = quantity.split('/').map(Number);
          quantity = numerator / denominator;
        } else {
          quantity = parseFloat(quantity);
        }

        if (!isNaN(quantity)) {
          const adjustedQuantity = quantity * servingSize;

          // Format the adjusted quantity nicely
          let formattedQuantity;
          if (adjustedQuantity % 1 === 0) {
            formattedQuantity = adjustedQuantity.toString();
          } else if (adjustedQuantity < 1) {
            // Convert to fraction if less than 1
            const fraction = adjustedQuantity;
            if (fraction === 0.5) formattedQuantity = '1/2';
            else if (fraction === 0.25) formattedQuantity = '1/4';
            else if (fraction === 0.75) formattedQuantity = '3/4';
            else if (fraction === 0.33) formattedQuantity = '1/3';
            else if (fraction === 0.67) formattedQuantity = '2/3';
            else formattedQuantity = adjustedQuantity.toFixed(2);
          } else {
            formattedQuantity = adjustedQuantity.toFixed(1).replace('.0', '');
          }

          return `${formattedQuantity}${unit ? ' ' + unit : ''} ${item}`.trim();
        }
      }
    }

    // If no pattern matches, return original ingredient
    return ingredient;
  };

  const handleCreateMealPlans = async () => {
    if (selectedDates.length === 0 || selectedMealTypes.length === 0) {
      Alert.alert('Selection Required', 'Please select at least one date and one meal type.');
      return;
    }

    try {
      setIsCreatingMealPlan(true);

      // Create meal plans for each combination of date and meal type
      const promises = [];
      for (const date of selectedDates) {
        for (const mealType of selectedMealTypes) {
          const mealId = getMealIdForBackend(meal);

          promises.push(
            mealPlansAPI.createOrUpdateMealPlan({
              date: date,
              mealType: mealType,
              meal: mealId  // Send the meal ID
            })
          );

          // Also track for history
          promises.push(
            userAPI.addRecentlyAddedToMealPlan({
              meal,
              addedToDate: date,
              addedToMealType: mealType
            })
          );
        }
      }

      await Promise.all(promises);

      // Close modal and reset state
      setShowMealPicker(false);
      setSelectedDates([]);
      setSelectedMealTypes([]);

      // Show success message
      const dateText = selectedDates.length === 1 ? selectedDates[0] : `${selectedDates.length} dates`;
      const mealTypeText = selectedMealTypes.length === 1 ? selectedMealTypes[0] : `${selectedMealTypes.length} meal types`;

      Alert.alert(
        'Success!',
        `${meal?.name || 'Meal'} has been added to ${mealTypeText} for ${dateText}`,
        [
          {
            text: 'View Meal Plans',
            onPress: () => navigation.navigate('MealPlans')
          },
          { text: 'OK' }
        ]
      );
    } catch (error) {
      console.error('Error creating meal plans:', error);
      Alert.alert('Error', 'Failed to create meal plans. Please try again.');
    } finally {
      setIsCreatingMealPlan(false);
    }
  };

  const renderTabButton = (tabKey, title, icon) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tabKey && styles.tabButtonActive
      ]}
      onPress={() => setActiveTab(tabKey)}
    >
      <Ionicons 
        name={icon} 
        size={20} 
        color={activeTab === tabKey ? colors.primary : colors.textSecondary} 
      />
      <Text style={[
        styles.tabButtonText,
        activeTab === tabKey && styles.tabButtonTextActive
      ]}>
        {title}
      </Text>
    </TouchableOpacity>
  );

  const renderOverview = () => (
    <View style={styles.tabContent}>
      <Text style={styles.description}>
        {meal.description || 'A delicious Filipino dish that brings authentic flavors to your table.'}
      </Text>
      
      {/* Nutrition Info */}
      <View style={styles.nutritionSection}>
        <Text style={styles.sectionTitle}>Nutrition Information</Text>
        <Text style={styles.nutritionSubtitle}>Per {servingSize} serving{servingSize > 1 ? 's' : ''}</Text>
        <View style={styles.nutritionGrid}>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>{Math.round((meal.calories || 250) * servingSize)}</Text>
            <Text style={styles.nutritionLabel}>Calories</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>
              {meal.protein ? `${Math.round(parseFloat(meal.protein) * servingSize)}g` : `${Math.round(15 * servingSize)}g`}
            </Text>
            <Text style={styles.nutritionLabel}>Protein</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>
              {meal.carbs ? `${Math.round(parseFloat(meal.carbs) * servingSize)}g` : `${Math.round(30 * servingSize)}g`}
            </Text>
            <Text style={styles.nutritionLabel}>Carbs</Text>
          </View>
          <View style={styles.nutritionItem}>
            <Text style={styles.nutritionValue}>
              {meal.fat ? `${Math.round(parseFloat(meal.fat) * servingSize)}g` : `${Math.round(8 * servingSize)}g`}
            </Text>
            <Text style={styles.nutritionLabel}>Fat</Text>
          </View>
        </View>
      </View>

      {/* Meal Info */}
      <View style={styles.infoSection}>
        <View style={styles.infoRow}>
          <Ionicons name="time-outline" size={20} color={colors.primary} />
          <Text style={styles.infoText}>
            Prep Time: {meal.prepTime || '30 minutes'}
          </Text>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="people-outline" size={20} color={colors.primary} />
          <Text style={styles.infoText}>
            Original Serves: {meal.servings || '4 people'}
          </Text>
        </View>

        {/* Serving Size Controls */}
        <View style={styles.servingSizeContainer}>
          <View style={styles.servingSizeHeader}>
            <Ionicons name="restaurant-outline" size={20} color={colors.primary} />
            <Text style={styles.servingSizeLabel}>Serving Size</Text>
          </View>
          <View style={styles.servingSizeControls}>
            <TouchableOpacity
              style={[styles.servingButton, servingSize <= 1 && styles.servingButtonDisabled]}
              onPress={decrementServingSize}
              disabled={servingSize <= 1}
            >
              <Ionicons name="remove" size={20} color={servingSize <= 1 ? colors.textSecondary : colors.primary} />
            </TouchableOpacity>
            <View style={styles.servingSizeDisplay}>
              <Text style={styles.servingSizeText}>
                {servingSize} serving{servingSize > 1 ? 's' : ''}
              </Text>
            </View>
            <TouchableOpacity
              style={[styles.servingButton, servingSize >= 4 && styles.servingButtonDisabled]}
              onPress={incrementServingSize}
              disabled={servingSize >= 4}
            >
              <Ionicons name="add" size={20} color={servingSize >= 4 ? colors.textSecondary : colors.primary} />
            </TouchableOpacity>
          </View>
        </View>
        <View style={styles.infoRow}>
          <Ionicons name="flame-outline" size={20} color={colors.primary} />
          <Text style={styles.infoText}>
            Difficulty: {meal.difficulty || 'Medium'}
          </Text>
        </View>
      </View>
    </View>
  );

  const renderIngredients = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>
        Ingredients {servingSize > 1 ? `(for ${servingSize} servings)` : ''}
      </Text>
      {meal.ingredients && meal.ingredients.length > 0 ? (
        meal.ingredients.map((ingredient, index) => (
          <View key={index} style={styles.ingredientItem}>
            <View style={styles.ingredientBullet} />
            <Text style={styles.ingredientText}>
              {adjustIngredientQuantity(ingredient)}
            </Text>
          </View>
        ))
      ) : (
        <Text style={styles.placeholderText}>
          Ingredients list will be available soon.
        </Text>
      )}
    </View>
  );

  const renderInstructions = () => (
    <View style={styles.tabContent}>
      <Text style={styles.sectionTitle}>Instructions</Text>
      {meal.instructions && meal.instructions.length > 0 ? (
        meal.instructions.map((instruction, index) => (
          <View key={index} style={styles.instructionItem}>
            <View style={styles.stepNumber}>
              <Text style={styles.stepNumberText}>{index + 1}</Text>
            </View>
            <Text style={styles.instructionText}>{instruction}</Text>
          </View>
        ))
      ) : (
        <Text style={styles.placeholderText}>
          Cooking instructions will be available soon.
        </Text>
      )}
    </View>
  );

  // Quick Meal Picker Modal Component
  const renderQuickMealPicker = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const dayAfterTomorrow = new Date(today);
    dayAfterTomorrow.setDate(dayAfterTomorrow.getDate() + 2);

    const formatDate = (date) => date.toISOString().split('T')[0];
    const formatDisplayDate = (date) => {
      const options = { weekday: 'short', month: 'short', day: 'numeric' };
      return date.toLocaleDateString('en-US', options);
    };

    const mealTypes = [
      { key: 'breakfast', label: 'Breakfast', icon: 'sunny-outline' },
      { key: 'lunch', label: 'Lunch', icon: 'partly-sunny-outline' },
      { key: 'dinner', label: 'Dinner', icon: 'moon-outline' },
      { key: 'snack', label: 'Snack', icon: 'cafe-outline' }
    ];

    const dates = [
      { key: formatDate(today), label: `Today (${formatDisplayDate(today)})` },
      { key: formatDate(tomorrow), label: `Tomorrow (${formatDisplayDate(tomorrow)})` },
      { key: formatDate(dayAfterTomorrow), label: formatDisplayDate(dayAfterTomorrow) }
    ];

    const canCreate = selectedDates.length > 0 && selectedMealTypes.length > 0;

    return (
      <Modal
        visible={showMealPicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowMealPicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            {/* Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Add to Meal Plan</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowMealPicker(false)}
              >
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            </View>

            <ScrollView
              style={styles.modalScrollView}
              showsVerticalScrollIndicator={false}
              bounces={false}
            >
            {/* Meal Info */}
            <View style={styles.modalMealInfo}>
              <Image
                source={{ uri: meal.image || 'https://via.placeholder.com/80x60' }}
                style={styles.modalMealImage}
                resizeMode="cover"
              />
              <View style={styles.modalMealDetails}>
                <Text style={styles.modalMealName} numberOfLines={2}>
                  {meal?.name || 'Meal'}
                </Text>
                <Text style={styles.modalMealCalories}>
                  {meal.calories || 0} calories
                </Text>
              </View>
            </View>

            {/* Date Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Dates ({selectedDates.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {dates.map((date) => {
                  const isSelected = selectedDates.includes(date.key);
                  return (
                    <TouchableOpacity
                      key={date.key}
                      style={[
                        styles.modalDateOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleDateSelection(date.key)}
                    >
                      <Ionicons
                        name={isSelected ? "calendar" : "calendar-outline"}
                        size={20}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {date.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Meal Type Selection */}
            <View style={styles.modalSection}>
              <Text style={styles.modalSectionTitle}>
                Select Meal Types ({selectedMealTypes.length} selected)
              </Text>
              <View style={styles.modalOptionsGrid}>
                {mealTypes.map((mealType) => {
                  const isSelected = selectedMealTypes.includes(mealType.key);
                  return (
                    <TouchableOpacity
                      key={mealType.key}
                      style={[
                        styles.modalMealTypeOption,
                        isSelected && styles.modalOptionSelected
                      ]}
                      onPress={() => toggleMealTypeSelection(mealType.key)}
                    >
                      <Ionicons
                        name={mealType.icon}
                        size={24}
                        color={isSelected ? colors.surface : colors.primary}
                      />
                      <Text style={[
                        styles.modalOptionText,
                        isSelected && styles.modalOptionTextSelected
                      ]}>
                        {mealType.label}
                      </Text>
                      {isSelected && (
                        <Ionicons
                          name="checkmark-circle"
                          size={16}
                          color={colors.surface}
                          style={styles.modalCheckmark}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            </View>

            {/* Action Buttons */}
            <View style={styles.modalActions}>
              {/* Create Meal Plans Button */}
              <TouchableOpacity
                style={[
                  styles.modalCreateButton,
                  !canCreate && styles.modalCreateButtonDisabled
                ]}
                onPress={handleCreateMealPlans}
                disabled={!canCreate || isCreatingMealPlan}
              >
                {isCreatingMealPlan ? (
                  <ActivityIndicator size="small" color={colors.surface} />
                ) : (
                  <>
                    <Ionicons name="add-circle" size={20} color={colors.surface} />
                    <Text style={styles.modalCreateButtonText}>
                      Create Meal Plans
                    </Text>
                  </>
                )}
              </TouchableOpacity>

              {/* Advanced Options */}
              <TouchableOpacity
                style={styles.modalAdvancedButton}
                onPress={() => {
                  setShowMealPicker(false);
                  setSelectedDates([]);
                  setSelectedMealTypes([]);
                  navigation.navigate('MealPlans', {
                    screen: 'CreateMealPlan',
                    params: { selectedMeal: meal }
                  });
                }}
              >
                <Ionicons name="settings-outline" size={16} color={colors.primary} />
                <Text style={styles.modalAdvancedText}>Advanced Options</Text>
              </TouchableOpacity>
            </View>
            </ScrollView>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.surface} />
        </TouchableOpacity>
        <Text style={styles.headerTitle} numberOfLines={1}>
          {meal?.name || 'Meal Details'}
        </Text>
        <TouchableOpacity 
          style={styles.headerButton}
          onPress={handleFavoritePress}
        >
          <Ionicons 
            name={isFavorite(meal.id || meal._id) ? "heart" : "heart-outline"} 
            size={24} 
            color={colors.surface} 
          />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.container}>
        {/* Hero Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: meal.image || 'https://via.placeholder.com/400x250' }}
            style={styles.heroImage}
            resizeMode="cover"
          />
          <View style={styles.imageOverlay}>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={styles.ratingText}>{meal.rating || '4.5'}</Text>
            </View>
          </View>
        </View>

        {/* Meal Title and Category */}
        <View style={styles.titleSection}>
          <Text style={styles.mealTitle}>{meal?.name || 'Meal Details'}</Text>
          <View style={styles.categoryContainer}>
            <Text style={styles.categoryText}>
              {meal?.category ? (Array.isArray(meal.category) ? meal.category.join(', ') : meal.category) : 'Filipino Cuisine'}
            </Text>
          </View>

          {/* Meal Tags */}
          <View style={styles.tagsSection}>
            {/* Meal Type Tags */}
            {meal?.mealType && meal.mealType.length > 0 && (
              <View style={styles.tagGroup}>
                {meal.mealType.map((type, index) => (
                  <View key={`mealType-${index}`} style={[styles.tag, styles.mealTypeTag]}>
                    <Ionicons name="time-outline" size={12} color={colors.primary} />
                    <Text style={styles.tagText}>{type}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Dietary Tags */}
            {meal?.dietaryTags && meal.dietaryTags.length > 0 && (
              <View style={styles.tagGroup}>
                {meal.dietaryTags.map((tag, index) => (
                  <View key={`dietary-${index}`} style={[styles.tag, styles.dietaryTag]}>
                    <Ionicons name="leaf-outline" size={12} color={colors.success || colors.primary} />
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* General Tags */}
            {meal?.tags && meal.tags.length > 0 && (
              <View style={styles.tagGroup}>
                {meal.tags.map((tag, index) => (
                  <View key={`tag-${index}`} style={[styles.tag, styles.generalTag]}>
                    <Ionicons name="pricetag-outline" size={12} color={colors.textSecondary} />
                    <Text style={styles.tagText}>{tag}</Text>
                  </View>
                ))}
              </View>
            )}

            {/* Allergen Tags */}
            {meal?.allergens && meal.allergens.length > 0 && (
              <View style={styles.tagGroup}>
                {meal.allergens.map((allergen, index) => (
                  <View key={`allergen-${index}`} style={[styles.tag, styles.allergenTag]}>
                    <Ionicons name="warning-outline" size={12} color={colors.secondary} />
                    <Text style={styles.tagText}>{allergen}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        </View>

        {/* Tabs */}
        <View style={styles.tabsContainer}>
          {renderTabButton('overview', 'Overview', 'information-circle-outline')}
          {renderTabButton('ingredients', 'Ingredients', 'list-outline')}
          {renderTabButton('instructions', 'Instructions', 'book-outline')}
        </View>

        {/* Tab Content */}
        {activeTab === 'overview' && renderOverview()}
        {activeTab === 'ingredients' && renderIngredients()}
        {activeTab === 'instructions' && renderInstructions()}
      </ScrollView>

      {/* Bottom Action Bar */}
      <View style={styles.bottomBar}>
        <TouchableOpacity 
          style={styles.addToPlanButton}
          onPress={handleAddToPlan}
        >
          <Ionicons name="calendar-outline" size={20} color={colors.surface} />
          <Text style={styles.addToPlanButtonText}>Add to Meal</Text>
        </TouchableOpacity>
      </View>

      {/* Quick Meal Picker Modal */}
      {renderQuickMealPicker()}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerButton: {
    padding: spacing.sm,
  },
  headerTitle: {
    flex: 1,
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.surface,
    textAlign: 'center',
    marginHorizontal: spacing.md,
  },
  imageContainer: {
    position: 'relative',
  },
  heroImage: {
    width: width,
    height: 250,
  },
  imageOverlay: {
    position: 'absolute',
    bottom: spacing.md,
    right: spacing.md,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.medium,
  },
  ratingText: {
    color: colors.surface,
    fontSize: fonts.sizes.small,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  titleSection: {
    padding: spacing.md,
    backgroundColor: colors.surface,
  },
  mealTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.sm,
  },
  categoryContainer: {
    alignSelf: 'flex-start',
  },
  categoryText: {
    fontSize: fonts.sizes.medium,
    color: colors.primary,
    fontWeight: '500',
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonActive: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginLeft: spacing.xs,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: colors.primary,
  },
  tabContent: {
    padding: spacing.md,
    backgroundColor: colors.surface,
    minHeight: 300,
  },
  description: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    lineHeight: 24,
    marginBottom: spacing.lg,
  },
  sectionTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
    marginBottom: spacing.md,
  },
  nutritionSection: {
    marginBottom: spacing.lg,
  },
  nutritionGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: colors.background,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
  },
  nutritionItem: {
    alignItems: 'center',
  },
  nutritionValue: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.primary,
  },
  nutritionLabel: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  infoSection: {
    marginBottom: spacing.lg,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  infoText: {
    fontSize: fonts.sizes.medium,
    color: colors.text,
    marginLeft: spacing.md,
  },
  ingredientItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  ingredientBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: colors.primary,
    marginTop: 8,
    marginRight: spacing.md,
  },
  ingredientText: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    lineHeight: 22,
  },
  instructionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
  },
  stepNumber: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  stepNumberText: {
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    color: colors.surface,
  },
  instructionText: {
    flex: 1,
    fontSize: fonts.sizes.medium,
    color: colors.text,
    lineHeight: 22,
  },
  placeholderText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    marginTop: spacing.lg,
  },
  bottomBar: {
    backgroundColor: colors.surface,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border,
  },
  addToPlanButton: {
    backgroundColor: colors.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.md,
    borderRadius: borderRadius.medium,
  },
  addToPlanButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
    marginLeft: spacing.sm,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.surface,
    borderTopLeftRadius: borderRadius.large,
    borderTopRightRadius: borderRadius.large,
    paddingHorizontal: spacing.lg,
    paddingBottom: spacing.xl,
    maxHeight: '85%',
    minHeight: '60%',
  },
  modalScrollView: {
    flex: 1,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.text,
  },
  modalCloseButton: {
    padding: spacing.sm,
  },
  modalMealInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  modalMealImage: {
    width: 60,
    height: 45,
    borderRadius: borderRadius.small,
    marginRight: spacing.md,
  },
  modalMealDetails: {
    flex: 1,
  },
  modalMealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.xs,
  },
  modalMealCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
  },
  modalSection: {
    paddingVertical: spacing.lg,
  },
  modalSectionTitle: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginBottom: spacing.md,
  },
  modalOptionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  modalDateOption: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 50,
  },
  modalMealTypeOption: {
    width: '48%',
    alignItems: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
    minHeight: 70,
    position: 'relative',
  },
  modalOptionSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  modalOptionText: {
    fontSize: fonts.sizes.small,
    color: colors.text,
    fontWeight: '500',
    marginLeft: spacing.sm,
    flex: 1,
  },
  modalOptionTextSelected: {
    color: colors.surface,
  },
  modalCheckmark: {
    position: 'absolute',
    top: spacing.xs,
    right: spacing.xs,
  },
  modalActions: {
    paddingTop: spacing.lg,
    gap: spacing.md,
  },
  modalCreateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.primary,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    minHeight: 50,
  },
  modalCreateButtonDisabled: {
    backgroundColor: colors.textSecondary,
    opacity: 0.6,
  },
  modalCreateButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    fontWeight: '600',
    marginLeft: spacing.sm,
  },
  modalAdvancedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background,
    padding: spacing.md,
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
    marginTop: spacing.md,
  },
  modalAdvancedText: {
    fontSize: fonts.sizes.small,
    color: colors.primary,
    fontWeight: '500',
    marginLeft: spacing.xs,
  },
  // Serving size styles
  servingSizeContainer: {
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginVertical: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  servingSizeHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  servingSizeLabel: {
    fontSize: fonts.sizes.medium,
    fontWeight: '600',
    color: colors.text,
    marginLeft: spacing.sm,
  },
  servingSizeControls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: spacing.lg,
  },
  servingButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.surface,
    borderWidth: 2,
    borderColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  servingButtonDisabled: {
    borderColor: colors.textSecondary,
    opacity: 0.5,
  },
  servingSizeDisplay: {
    minWidth: 120,
    alignItems: 'center',
  },
  servingSizeText: {
    fontSize: fonts.sizes.large,
    fontWeight: '600',
    color: colors.text,
    textAlign: 'center',
  },
  // Tags styles
  tagsSection: {
    marginTop: spacing.md,
  },
  tagGroup: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: borderRadius.large,
    marginRight: spacing.sm,
    marginBottom: spacing.xs,
    borderWidth: 1,
  },
  mealTypeTag: {
    backgroundColor: colors.primary + '15',
    borderColor: colors.primary,
  },
  dietaryTag: {
    backgroundColor: (colors.success || colors.primary) + '15',
    borderColor: colors.success || colors.primary,
  },
  generalTag: {
    backgroundColor: colors.background,
    borderColor: colors.border,
  },
  allergenTag: {
    backgroundColor: colors.secondary + '15',
    borderColor: colors.secondary,
  },
  tagText: {
    fontSize: fonts.sizes.small,
    fontWeight: '500',
    marginLeft: spacing.xs,
    color: colors.text,
    textTransform: 'capitalize',
  },
  // Nutrition subtitle
  nutritionSubtitle: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginBottom: spacing.md,
    fontStyle: 'italic',
  },
});

export default MealDetailScreen;
