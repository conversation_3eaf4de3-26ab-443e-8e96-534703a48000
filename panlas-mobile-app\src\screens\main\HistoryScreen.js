import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  FlatList,
  TouchableOpacity,
  Image,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useFocusEffect } from '@react-navigation/native';
import { userAPI } from '../../services/api';
import { useAuth } from '../../context/AuthContext';
import { colors, legacyFonts as fonts, legacySpacing as spacing, borderRadius } from '../../styles/theme';
import { commonStyles } from '../../styles/commonStyles';

const HistoryScreen = ({ navigation }) => {
  const [recentlyViewed, setRecentlyViewed] = useState([]);
  const [recentlyAddedToMealPlans, setRecentlyAddedToMealPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [activeTab, setActiveTab] = useState('viewed');

  const { user } = useAuth();

  useEffect(() => {
    loadHistoryData();
  }, []);

  // Refresh history data when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      loadHistoryData();
    }, [])
  );

  const loadHistoryData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadRecentlyViewed(),
        loadRecentlyAddedToMealPlans(),
      ]);
    } catch (error) {
      console.error('Error loading history data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRecentlyViewed = async () => {
    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS ===');
      const response = await userAPI.getRecentlyViewedMeals();
      console.log('Recently viewed meals API response:', JSON.stringify(response, null, 2));

      // Handle different response structures
      let meals = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          meals = response.data;
          console.log('Response data is array, using directly');
        } else if (response.data.recentlyViewedMeals) {
          meals = response.data.recentlyViewedMeals;
          console.log('Response data has recentlyViewedMeals property');
        } else {
          console.log('Response data structure not recognized:', response.data);
        }
      } else {
        console.log('No response.data found');
      }

      console.log('Final meals array:', JSON.stringify(meals, null, 2));
      console.log('Setting recently viewed meals count:', meals.length);
      setRecentlyViewed(meals);
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      console.error('Error details:', JSON.stringify(error, null, 2));
    }
  };

  const loadRecentlyAddedToMealPlans = async () => {
    try {
      const response = await userAPI.getRecentlyAddedToMealPlans();
      console.log('Recently added to meal plans API response:', response);

      // Handle different response structures
      let meals = [];
      if (response.data) {
        if (Array.isArray(response.data)) {
          meals = response.data;
        } else if (response.data.recentlyAddedToMealPlans) {
          meals = response.data.recentlyAddedToMealPlans;
        }
      }

      console.log('Setting recently added to meal plans:', meals);
      setRecentlyAddedToMealPlans(meals);
    } catch (error) {
      console.error('Error loading recently added to meal plans:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHistoryData();
    setRefreshing(false);
  };

  const handleMealPress = (meal) => {
    console.log('History: Navigating to meal detail with data:', {
      id: meal.id || meal._id || meal.mealId,
      name: meal.name,
      hasIngredients: !!meal.ingredients,
      hasInstructions: !!meal.instructions,
      dataKeys: Object.keys(meal)
    });
    navigation.navigate('MealDetail', { meal });
  };

  const renderMealItem = ({ item: meal }) => (
    <TouchableOpacity
      style={styles.mealItem}
      onPress={() => handleMealPress(meal)}
    >
      <Image
        source={{ uri: meal.image || 'https://via.placeholder.com/80x60' }}
        style={styles.mealImage}
        resizeMode="cover"
      />
      <View style={styles.mealInfo}>
        <Text style={styles.mealName} numberOfLines={2}>
          {meal.name}
        </Text>
        <Text style={styles.mealCategory}>
          {Array.isArray(meal.category) ? meal.category[0] : meal.category}
        </Text>
        <Text style={styles.mealCalories}>
          {meal.calories || 0} calories
        </Text>
      </View>
      <View style={styles.mealMeta}>
        <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const renderMealPlanItem = ({ item: meal }) => (
    <TouchableOpacity
      style={styles.mealItem}
      onPress={() => handleMealPress(meal)}
    >
      <Image
        source={{ uri: meal.image || 'https://via.placeholder.com/80x60' }}
        style={styles.mealImage}
        resizeMode="cover"
      />
      <View style={styles.mealInfo}>
        <Text style={styles.mealName} numberOfLines={2}>
          {meal.name}
        </Text>
        <Text style={styles.mealCategory}>
          Added to {meal.addedToMealType} • {new Date(meal.addedToDate).toLocaleDateString()}
        </Text>
        <Text style={styles.mealCalories}>
          {meal.calories || 0} calories
        </Text>
      </View>
      <View style={styles.mealMeta}>
        <Ionicons name="calendar-outline" size={20} color={colors.primary} />
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={commonStyles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>History</Text>
        <Text style={styles.headerSubtitle}>Your meal activity</Text>
      </View>

      {/* Tabs */}
      <View style={styles.tabsContainer}>
        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'viewed' && styles.tabButtonActive]}
          onPress={() => setActiveTab('viewed')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'viewed' && styles.tabButtonTextActive
          ]}>
            Recently Viewed
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.tabButton, activeTab === 'added' && styles.tabButtonActive]}
          onPress={() => setActiveTab('added')}
        >
          <Text style={[
            styles.tabButtonText,
            activeTab === 'added' && styles.tabButtonTextActive
          ]}>
            Added to Meal Plans
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      {loading ? (
        <View style={commonStyles.loadingContainer}>
          <Text style={commonStyles.loadingText}>Loading history...</Text>
        </View>
      ) : (
        <FlatList
          data={activeTab === 'viewed' ? recentlyViewed : recentlyAddedToMealPlans}
          renderItem={activeTab === 'viewed' ? renderMealItem : renderMealPlanItem}
          keyExtractor={(item, index) => `${item.id || item._id || index}`}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
            />
          }
          ListEmptyComponent={
            <View style={styles.emptyContainer}>
              <Ionicons
                name={activeTab === 'viewed' ? "eye-outline" : "calendar-outline"}
                size={64}
                color={colors.textSecondary}
              />
              <Text style={styles.emptyTitle}>
                {activeTab === 'viewed' ? 'No recently viewed meals' : 'No meals added to plans'}
              </Text>
              <Text style={styles.emptySubtitle}>
                {activeTab === 'viewed'
                  ? 'Start browsing meals to see your viewing history'
                  : 'Add meals to your meal plans to see them here'
                }
              </Text>
              <TouchableOpacity
                style={styles.browseButton}
                onPress={() => navigation.navigate('Home')}
              >
                <Text style={styles.browseButtonText}>Browse Meals</Text>
              </TouchableOpacity>
            </View>
          }
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  header: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  headerTitle: {
    fontSize: fonts.sizes.xlarge,
    fontWeight: 'bold',
    color: colors.surface,
  },
  headerSubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.surface,
    opacity: 0.8,
    marginTop: spacing.xs,
  },
  tabsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  tabButton: {
    flex: 1,
    paddingVertical: spacing.md,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  tabButtonActive: {
    borderBottomColor: colors.primary,
  },
  tabButtonText: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    fontWeight: '500',
  },
  tabButtonTextActive: {
    color: colors.primary,
  },
  listContainer: {
    padding: spacing.md,
  },
  mealItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    borderRadius: borderRadius.medium,
    padding: spacing.md,
    marginBottom: spacing.md,
    ...commonStyles.shadowSmall,
  },
  mealImage: {
    width: 60,
    height: 45,
    borderRadius: borderRadius.small,
  },
  mealInfo: {
    flex: 1,
    marginLeft: spacing.md,
  },
  mealName: {
    fontSize: fonts.sizes.medium,
    fontWeight: '500',
    color: colors.text,
  },
  mealCategory: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  mealCalories: {
    fontSize: fonts.sizes.small,
    color: colors.textSecondary,
    marginTop: spacing.xs,
  },
  mealMeta: {
    padding: spacing.sm,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: spacing.xxl,
    paddingHorizontal: spacing.lg,
  },
  emptyTitle: {
    fontSize: fonts.sizes.large,
    fontWeight: 'bold',
    color: colors.textSecondary,
    marginTop: spacing.md,
  },
  emptySubtitle: {
    fontSize: fonts.sizes.medium,
    color: colors.textSecondary,
    marginTop: spacing.sm,
    textAlign: 'center',
    lineHeight: 22,
  },
  browseButton: {
    backgroundColor: colors.primary,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: borderRadius.medium,
    marginTop: spacing.lg,
  },
  browseButtonText: {
    color: colors.surface,
    fontSize: fonts.sizes.medium,
    fontWeight: 'bold',
  },
});

export default HistoryScreen;
